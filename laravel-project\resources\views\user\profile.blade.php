@extends('layouts.user')

@section('title', '<PERSON><PERSON> Sơ Cá Nhân')

@section('content')
<div class="row">
    <div class="col-12 mb-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('user.dashboard') }}">Bảng Đi<PERSON></a></li>
                <li class="breadcrumb-item active">Hồ Sơ Cá Nhân</li>
            </ol>
        </nav>
        <h1>Hồ Sơ Cá Nhân</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="profile-avatar mb-3">
                    <div class="avatar-xl bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center">
                        {{ strtoupper(substr(Auth::user()->name, 0, 2)) }}
                    </div>
                </div>
                <h4>{{ Auth::user()->name }}</h4>
                <p class="text-muted">{{ Auth::user()->email }}</p>
                <span class="badge bg-primary mb-3">
                    {{ Auth::user()->role === 'admin' ? 'Quản Trị Viên' : 'Người Dùng' }}
                </span>
                
                <div class="d-grid gap-2">
                    <a href="{{ route('profile.edit') }}" class="btn btn-primary">
                        Chỉnh Sửa Hồ Sơ
                    </a>
                    <button class="btn btn-outline-secondary">
                        Thay Đổi Ảnh Đại Diện
                    </button>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Thống Kê Tài Khoản</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        <h4 class="text-primary">{{ Auth::user()->created_at->diffInDays(now()) }}</h4>
                        <small class="text-muted">Ngày Tham Gia Hệ Thống</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h5 class="text-success">Hoạt Động</h5>
                        <small class="text-muted">Trạng Thái Tài Khoản</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-info">Đã Xác Thực</h5>
                        <small class="text-muted">Email Đã Xác Nhận</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Thông Tin Chi Tiết</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Tên Đầy Đủ</label>
                        <div class="form-control-plaintext fw-bold">{{ Auth::user()->name }}</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Địa Chỉ Email</label>
                        <div class="form-control-plaintext fw-bold">{{ Auth::user()->email }}</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Vai Trò Trong Hệ Thống</label>
                        <div class="form-control-plaintext">
                            <span class="badge bg-primary">
                                {{ Auth::user()->role === 'admin' ? 'Quản Trị Viên' : 'Người Dùng' }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Ngày Tham Gia</label>
                        <div class="form-control-plaintext fw-bold">{{ Auth::user()->created_at->format('d/m/Y H:i') }}</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Lần Cập Nhật Cuối</label>
                        <div class="form-control-plaintext fw-bold">{{ Auth::user()->updated_at->format('d/m/Y H:i') }}</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Trạng Thái Email</label>
                        <div class="form-control-plaintext">
                            @if(Auth::user()->email_verified_at)
                                <span class="badge bg-success">Đã Xác Thực</span>
                            @else
                                <span class="badge bg-warning">Chưa Xác Thực</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Cài Đặt Bảo Mật</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Xác Thực Hai Bước</h6>
                                <small class="text-muted">Tăng Cường Bảo Mật Tài Khoản</small>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="twoFactor">
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Thông Báo Email</h6>
                                <small class="text-muted">Nhận Thông Báo Qua Email</small>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <hr>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary">
                                Thay Đổi Mật Khẩu
                            </button>
                            <button class="btn btn-outline-secondary">
                                Xem Lịch Sử Đăng Nhập
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Hoạt Động Gần Đây</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Đăng Nhập Thành Công</h6>
                            <p class="text-muted mb-0">Đăng Nhập Vào Hệ Thống Từ Địa Chỉ IP: ***********</p>
                            <small class="text-muted">{{ now()->format('d/m/Y H:i') }}</small>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Cập Nhật Hồ Sơ</h6>
                            <p class="text-muted mb-0">Thông Tin Hồ Sơ Đã Được Cập Nhật</p>
                            <small class="text-muted">{{ Auth::user()->updated_at->format('d/m/Y H:i') }}</small>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Tài Khoản Được Tạo</h6>
                            <p class="text-muted mb-0">Tài Khoản Đã Được Tạo Và Kích Hoạt Thành Công</p>
                            <small class="text-muted">{{ Auth::user()->created_at->format('d/m/Y H:i') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Thao Tác Nhanh</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('profile.edit') }}" class="btn btn-primary w-100">
                            Chỉnh Sửa Thông Tin
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-primary w-100">
                            Thay Đổi Mật Khẩu
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-secondary w-100">
                            Tải Xuống Dữ Liệu
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-info w-100">
                            Liên Hệ Hỗ Trợ
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .avatar-xl {
        width: 80px;
        height: 80px;
        font-size: 32px;
        font-weight: 600;
    }
    
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e9ecef;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -23px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 0 2px #e9ecef;
    }
    
    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 3px solid #667eea;
    }
    
    .form-control-plaintext {
        padding: 0.375rem 0;
        margin-bottom: 0;
        font-size: 1rem;
        line-height: 1.5;
        color: #212529;
        background-color: transparent;
        border: solid transparent;
        border-width: 1px 0;
    }
</style>
@endpush
