@extends('layouts.admin')

@section('title', 'Bảng Đ<PERSON>ều <PERSON>ển Q<PERSON>ản Trị')

@section('breadcrumb')
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item active">Bảng Đ<PERSON></li>
        </ol>
    </nav>
@endsection

@section('content')
<!-- Welcome Card -->
<div class="card bg-primary-subtle mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h3 class="card-title text-primary mb-2">Chào Mừng Đến Với Bảng Điều Khiển Quản Trị</h3>
                <p class="card-text mb-3">Quản Lý Hệ Thống Và Người Dùng Một Cách Hiệu Quả</p>
                <a href="{{ route('admin.users') }}" class="btn btn-primary">
                    <iconify-icon icon="solar:users-group-two-rounded-line-duotone" class="me-2"></iconify-icon>
                    Qu<PERSON><PERSON><PERSON>ời Dùng
                </a>
            </div>
            <div class="col-md-4 text-center">
                <iconify-icon icon="solar:shield-user-line-duotone" class="fs-1 text-primary"></iconify-icon>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-center border-0 shadow-sm">
            <div class="card-body">
                <div class="mb-3">
                    <iconify-icon icon="solar:users-group-rounded-line-duotone" class="fs-1 text-primary"></iconify-icon>
                </div>
                <h4 class="text-primary mb-1">{{ \App\Models\User::count() }}</h4>
                <h6 class="card-title mb-0">Tổng Người Dùng</h6>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-center border-0 shadow-sm">
            <div class="card-body">
                <div class="mb-3">
                    <iconify-icon icon="solar:shield-user-line-duotone" class="fs-1 text-danger"></iconify-icon>
                </div>
                <h4 class="text-danger mb-1">{{ \App\Models\User::where('role', 'admin')->count() }}</h4>
                <h6 class="card-title mb-0">Quản Trị Viên</h6>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-center border-0 shadow-sm">
            <div class="card-body">
                <div class="mb-3">
                    <iconify-icon icon="solar:user-line-duotone" class="fs-1 text-success"></iconify-icon>
                </div>
                <h4 class="text-success mb-1">{{ \App\Models\User::where('role', 'user')->count() }}</h4>
                <h6 class="card-title mb-0">Người Dùng Thường</h6>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-center border-0 shadow-sm">
            <div class="card-body">
                <div class="mb-3">
                    <iconify-icon icon="solar:calendar-add-line-duotone" class="fs-1 text-warning"></iconify-icon>
                </div>
                <h4 class="text-warning mb-1">{{ \App\Models\User::whereDate('created_at', today())->count() }}</h4>
                <h6 class="card-title mb-0">Hoạt Động Hôm Nay</h6>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Người Dùng Mới Nhất</h5>
                <a href="{{ route('admin.users') }}" class="btn btn-primary btn-sm">
                    <iconify-icon icon="solar:eye-line-duotone" class="me-1"></iconify-icon>
                    Xem Tất Cả
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Tên</th>
                                <th>Email</th>
                                <th>Vai Trò</th>
                                <th>Ngày Tạo</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach(\App\Models\User::latest()->take(5)->get() as $user)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                            {{ strtoupper(substr($user->name, 0, 1)) }}
                                        </div>
                                        {{ $user->name }}
                                    </div>
                                </td>
                                <td>{{ $user->email }}</td>
                                <td>
                                    <span class="badge {{ $user->role === 'admin' ? 'bg-danger' : 'bg-primary' }}">
                                        {{ $user->role === 'admin' ? 'Quản Trị Viên' : 'Người Dùng' }}
                                    </span>
                                </td>
                                <td>{{ $user->created_at->format('d/m/Y H:i') }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Thông Tin Hệ Thống</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">Phiên Bản Laravel</small>
                    <div class="fw-semibold">{{ app()->version() }}</div>
                </div>

                <div class="mb-3">
                    <small class="text-muted">Phiên Bản PHP</small>
                    <div class="fw-semibold">{{ PHP_VERSION }}</div>
                </div>

                <div class="mb-3">
                    <small class="text-muted">Múi Giờ</small>
                    <div class="fw-semibold">{{ config('app.timezone') }}</div>
                </div>

                <div class="mb-3">
                    <small class="text-muted">Môi Trường</small>
                    <div class="fw-semibold">
                        <span class="badge {{ app()->environment('production') ? 'bg-success' : 'bg-warning' }}">
                            {{ ucfirst(app()->environment()) }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Thao Tác Nhanh</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.users') }}" class="btn btn-primary btn-sm">
                        <iconify-icon icon="solar:users-group-two-rounded-line-duotone" class="me-2"></iconify-icon>
                        Quản Lý Người Dùng
                    </a>
                    <a href="{{ route('user.dashboard') }}" class="btn btn-outline-primary btn-sm">
                        <iconify-icon icon="solar:home-line-duotone" class="me-2"></iconify-icon>
                        Xem Trang User
                    </a>
                    <button class="btn btn-outline-secondary btn-sm">
                        <iconify-icon icon="solar:settings-line-duotone" class="me-2"></iconify-icon>
                        Cài Đặt Hệ Thống
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
    .avatar-sm {
        width: 32px;
        height: 32px;
        font-size: 14px;
        font-weight: 600;
    }
</style>
@endpush
